/* ZonGrabber 样式文件 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f8f9fa;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 400px;
    margin: 0 auto;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #ff9900, #ff7700);
    color: white;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo img {
    width: 24px;
    height: 24px;
}

.logo h1 {
    font-size: 18px;
    font-weight: 600;
}

.version {
    font-size: 12px;
    opacity: 0.8;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

/* 状态栏 */
.status-bar {
    background: white;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6c757d;
    animation: pulse 2s infinite;
}

.status-dot.active {
    background: #28a745;
}

.status-dot.error {
    background: #dc3545;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    justify-content: center;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
}

/* 操作区域 */
.action-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
}

/* 商品预览 */
.product-preview {
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.product-preview h3 {
    margin-bottom: 12px;
    color: #333;
    font-size: 16px;
}

.preview-content {
    display: flex;
    gap: 12px;
}

.product-image img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.product-info {
    flex: 1;
}

.product-info h4 {
    font-size: 14px;
    margin-bottom: 8px;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-meta {
    display: flex;
    gap: 12px;
    margin-bottom: 8px;
    font-size: 12px;
    color: #6c757d;
}

.price-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.current-price {
    font-weight: 600;
    color: #dc3545;
}

.rating {
    font-size: 12px;
    color: #ffc107;
}

/* 数据详情 */
.data-details {
    background: white;
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* 标签页 */
.tabs {
    display: flex;
    border-bottom: 1px solid #e9ecef;
}

.tab-btn {
    flex: 1;
    padding: 12px 8px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 12px;
    color: #6c757d;
    transition: all 0.2s;
}

.tab-btn.active {
    color: #007bff;
    border-bottom: 2px solid #007bff;
}

.tab-content {
    padding: 16px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* 信息网格 */
.info-grid {
    display: grid;
    gap: 12px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.info-item label {
    font-weight: 500;
    color: #495057;
    min-width: 80px;
}

.info-item span {
    flex: 1;
    text-align: right;
    color: #333;
    word-break: break-word;
}

/* 评论样式 */
.reviews-summary, .reviews-list {
    margin-bottom: 16px;
}

.reviews-summary h4, .reviews-list h4 {
    margin-bottom: 8px;
    font-size: 14px;
}

.review-item {
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 8px;
}

.review-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 12px;
}

.review-content {
    font-size: 13px;
    line-height: 1.4;
}

/* 联盟信息样式 */
.affiliate-info {
    padding: 0;
}

/* 销售佣金特殊样式 */
#affiliateEarnings {
    font-size: 14px;
    font-weight: 600;
}

.info-item:has(#affiliateEarnings) {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 12px 8px;
    border: 1px solid #e9ecef;
    margin-top: 8px;
}

/* 变体信息样式 */
.variants-container {
    padding: 0;
}

.variant-item {
    margin-bottom: 12px;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.variant-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.variant-item strong {
    color: #495057;
    font-size: 13px;
    display: block;
    margin-bottom: 4px;
}

.variant-list {
    color: #333;
    font-size: 12px;
    line-height: 1.4;
    word-break: break-word;
}

/* 设置区域样式 */
.settings-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 20px;
    overflow: hidden;
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #e9ecef;
    cursor: pointer;
    user-select: none;
}

.settings-header:hover {
    background: #dee2e6;
}

.settings-header h3 {
    margin: 0;
    font-size: 14px;
    color: #495057;
    font-weight: 600;
}

.collapse-btn {
    background: none;
    border: none;
    font-size: 12px;
    color: #6c757d;
    cursor: pointer;
    transition: transform 0.2s;
}

.collapse-btn.collapsed {
    transform: rotate(-90deg);
}

.settings-content {
    padding: 16px;
    transition: all 0.3s ease;
}

.settings-content.collapsed {
    display: none;
}

.setting-item {
    margin-bottom: 12px;
}

.setting-item label {
    display: block;
    margin-bottom: 6px;
    font-size: 12px;
    font-weight: 500;
    color: #495057;
}

.input-group {
    display: flex;
    gap: 8px;
    align-items: center;
}

.input-group input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 12px;
    background: white;
}

.input-group input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.btn-small {
    padding: 8px 12px;
    font-size: 11px;
    min-width: auto;
}

.setting-note {
    margin-top: 6px;
}

.setting-note small {
    color: #6c757d;
    font-size: 11px;
    line-height: 1.3;
}

/* 联盟链接样式 */
.affiliate-url-item {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 12px 8px;
    border: 1px solid #e9ecef;
    margin-top: 8px;
}

.url-container {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-top: 6px;
}

.url-container input {
    flex: 1;
    padding: 6px 8px;
    border: 1px solid #ced4da;
    border-radius: 3px;
    font-size: 11px;
    font-family: 'Courier New', monospace;
    background: white;
    color: #495057;
}

.url-container input:focus {
    outline: none;
    border-color: #007bff;
}

.url-container .btn-small {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    padding: 8px 16px;
    font-size: 11px;
    font-weight: 600;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.url-container .btn-small:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}

.url-container .btn-small:active {
    transform: translateY(0);
}

.affiliate-url-item label {
    color: #495057;
    font-weight: 600;
    font-size: 12px;
}

/* 导出按钮样式 */
.btn-export {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 6px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.btn-export:hover {
    background: linear-gradient(135deg, #218838, #1ea080);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
}

.btn-export:active {
    transform: translateY(0);
}

.btn-export .btn-icon {
    font-size: 16px;
}

/* 佣金状态样式 */
.commission-status-good {
    color: #28a745 !important;
    font-weight: 600;
    background: #d4edda;
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid #c3e6cb;
}

.commission-status-bad {
    color: #dc3545 !important;
    font-weight: 600;
    background: #f8d7da;
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid #f5c6cb;
}

.commission-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 8px 12px;
    margin-top: 8px;
    font-size: 12px;
    color: #856404;
}

.commission-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    padding: 8px 12px;
    margin-top: 8px;
    font-size: 12px;
    color: #155724;
}

/* 商品预览中的佣金状态样式 */
.earnings-status {
    margin-top: 12px;
    padding: 12px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 700;
    text-align: center;
    border: 2px solid;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.earnings-status.good {
    background: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.earnings-status.bad {
    background: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.earnings-status .earnings-amount {
    font-size: 18px;
    font-weight: 800;
    margin: 0 4px;
}

.earnings-status .status-icon {
    font-size: 18px;
    margin-right: 6px;
}

/* 原始数据 */
.raw-data textarea {
    width: 100%;
    height: 200px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 12px;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    resize: vertical;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.action-buttons .btn {
    flex: 1;
}

/* 底部工具栏 */
.footer {
    background: white;
    border-top: 1px solid #e9ecef;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.saved-count {
    font-size: 12px;
    color: #6c757d;
}

.export-section {
    display: flex;
    gap: 4px;
}

/* 加载指示器 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #6c757d;
    font-size: 14px;
}

/* 消息提示 */
.message-toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 1001;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

.message-toast.success {
    background: #28a745;
}

.message-toast.error {
    background: #dc3545;
}

.message-toast.warning {
    background: #ffc107;
    color: #333;
}
